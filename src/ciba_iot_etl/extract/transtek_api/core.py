import httpx
import json

from contextlib import asynccontextmanager

from loguru import logger

from ciba_iot_etl.extract.transtek_api.common import (
    ACTIVATE_ERROR,
    HOST,
    DEVICE_SERVICE,
    MIO_ERROR,
    NETWORK_ERROR,
    DEVICE_NOT_FOUND_ERROR,
    TELEMETRY_DATES_ERROR_1,
    TELEMETRY_DATES_ERROR_2,
    UNKNOWN_ERROR,
    MioConnectError,
)
from ciba_iot_etl.models.db.transtek import Transtek


class MioConnectClient:
    def __init__(self, api_key: str):
        self.API_KEY = api_key
        self.headers = {
            "x-api-key": self.API_KEY,
            "content-type": "application/json",
        }

    @asynccontextmanager
    async def api_client(self):
        """Context manager that handles client setup and exception handling."""
        async with httpx.AsyncClient(headers=self.headers, base_url=HOST) as client:
            try:
                yield client
            except (
                httpx.TransportError,
                httpx.DecodingError,
                httpx.TooManyRedirects,
            ) as e:
                raise MioConnectError(NETWORK_ERROR) from e
            except httpx.HTTPStatusError as e:
                message = json.loads(e.response.content)["message"]
                raise MioConnectError(
                    f"{MIO_ERROR} [{e.response.status_code}] : {message}"
                ) from e
            except MioConnectError as me:
                raise me
            except Exception as ue:
                raise MioConnectError(UNKNOWN_ERROR) from ue

    async def list_devices(self):
        """List devices in the account."""
        async with self.api_client() as client:
            response = await client.get(f"{DEVICE_SERVICE}")
            response.raise_for_status()
            data = response.json()
            if "items" in data:
                return data["items"]
            else:
                raise MioConnectError(UNKNOWN_ERROR)

    async def get_device(self, device_id: str):
        """Get device properties by deviceId. Different device model may return different property set."""
        async with self.api_client() as client:
            response = await client.get(f"{DEVICE_SERVICE}/{device_id}")
            response.raise_for_status()
            data = response.json()
            return data

    async def get_device_by_imei(self, imei: str):
        """Get device properties by deviceId. Different device model may return different property set."""
        device = await Transtek.filter(imei=imei).first()
        if device:
            logger.info(f"Found device in database: {device.device_id} for IMEI {imei}")

            return await self.get_device(device_id=device.device_id)

        async with self.api_client() as client:
            devices_list = []
            next_token = ""

            async def get_devices(devices_list):
                param = ""
                if next_token and next_token != "":
                    param = f"?nextToken={next_token}"
                devices_response = await client.get(f"{DEVICE_SERVICE}{param}")
                devices_response.raise_for_status()
                json_response = devices_response.json()
                devices_list.extend(json_response["items"])
                return (
                    json_response["nextToken"] if "nextToken" in json_response else None
                )

            i = 0
            while next_token is not None and i < 100:
                i += 1
                next_token = await get_devices(devices_list)

            device = next((d for d in devices_list if d.get("imei") == imei), None)

            if device is None:
                raise MioConnectError(DEVICE_NOT_FOUND_ERROR)

            response = await client.get(f"{DEVICE_SERVICE}/{device['deviceId']}")
            response.raise_for_status()
            data = response.json()
            return data

    async def get_device_data_usage(self, device_id: str):
        """Get device data usage information"""
        async with self.api_client() as client:
            response = await client.get(f"{DEVICE_SERVICE}/{device_id}/dataUsage")
            response.raise_for_status()
            data = response.json()
            return data

    async def get_device_parameters(self, device_id: str):
        """Retrieve device parameters on MioConnect by deviceId, such as measure interval,
        medicine intake reminder for BPM, etc."""
        async with self.api_client() as client:
            response = await client.get(f"{DEVICE_SERVICE}/{device_id}/params")
            response.raise_for_status()
            data = response.json()
            return data

    async def activate_device(self, device_id: str):
        """Activate device by deviceId."""
        async with self.api_client() as client:
            response = await client.post(f"{DEVICE_SERVICE}/{device_id}/activate")
            response.raise_for_status()
            data = response.json()

            if "status" in data:
                if data["status"] == "true":
                    return data
                else:
                    raise MioConnectError(ACTIVATE_ERROR)
            else:
                raise MioConnectError(UNKNOWN_ERROR)

    async def deactivate_device(self, device_id: str):
        """Deactivate device by deviceId."""
        async with self.api_client() as client:
            response = await client.post(f"{DEVICE_SERVICE}/{device_id}/deactivate")
            response.raise_for_status()
            data = response.json()

            if "status" in data:
                if data["status"] == "true":
                    return data
                else:
                    raise MioConnectError(ACTIVATE_ERROR)
            else:
                raise MioConnectError(UNKNOWN_ERROR)

    async def get_device_telemetry(
        self, device_id: str, start_time: int, end_time: int
    ):
        """List telemetry data for device with deviceId, ranging from startTime to endTime.
        Date format is unix timestamp. Telemetry data format differs between different device models."""

        if not isinstance(start_time, int) or not isinstance(end_time, int):
            raise MioConnectError(TELEMETRY_DATES_ERROR_2)

        if start_time > end_time:
            raise MioConnectError(TELEMETRY_DATES_ERROR_1)

        async with self.api_client() as client:
            params = {
                "startTime": start_time,
                "endTime": end_time,
            }
            response = await client.post(
                f"{DEVICE_SERVICE}/{device_id}/telemetry", params=params
            )
            response.raise_for_status()
            data = response.json()
            return data
