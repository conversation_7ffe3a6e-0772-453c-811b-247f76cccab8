from fastapi import APIRouter, HTTPException, Security, Request
from fastapi.security import APIKeyHeader

from ciba_iot_etl.models.db.dexcom import <PERSON><PERSON>
from ciba_iot_etl.models.db.transtek import Transtek
from ciba_iot_etl.repositories.devices_repository import (
    MemberDevicesRepository,
)
from ciba_iot_etl.models.pydantic.common import ActivityDevice
from ciba_iot_etl.extract.fitbit_api.core import FitbitLoader
from ciba_iot_etl.extract.dexcom_api.core import DexcomLoader
from ciba_iot_etl.extract.withings_api.core import WithingsLoader
from app.auth.constants import X_AUTH_KEY
from app.log.logging import logger
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_state import MemberState
from app.pydantic_model.base import AuthResp
from app.pydantic_model.device_api import (
    <PERSON>ceStatus,
    ConnectionStatus,
    ConnectionToken,
    MemberDeviceStatus,
    Subscription,
)
from ciba_iot_etl.helpers.crypto import generate_unique_state
from app.routers.requests.api_request import (
    DisconnectRequets,
    SyncRequets,
    StatusRequest,
)
from app.services.user import (
    User,
    UserDeviceService,
)
from app.services.utils.devices import check_health
from app.settings import get_settings
from app.services.withings import MemberWithings
from app.services.fitbit import MemberFitbit
import pendulum
from uuid import uuid4

settings = get_settings()

router = APIRouter(
    prefix="/devices",
    tags=["api-router"],
    dependencies=[
        # Depends(verify_token),
        Security(APIKeyHeader(name=X_AUTH_KEY)),
    ],
)


@router.get("/get_status")
async def get_status(
    type_device: ActivityDevice,
    member_type: str,
    member_id: str,
) -> ConnectionStatus:
    """
    Route to get the connection status of a device
    based on the user id and device type
    """
    logger.info(  # pylint: disable=logging-fstring-interpolation
        f"start processing get_status for device {type_device} from "
        f"{member_type} with id {member_id}"
    )

    try:
        member = await Member.get_by_platform(
            platform_type=member_type,
            platform_id=member_id,
        )
    except Exception as error:
        detail = f"Member data error: {error}"
        raise HTTPException(500, detail) from error

    if not member:
        raise HTTPException(404, "No member found with the provided id")

    user = User(email=member.email, device=type_device)

    try:
        device = await user.related_device()
    except Exception as error:
        detail = f"Device data error: {error}"
        raise HTTPException(500, detail) from error

    if device:
        logger.info(  # pylint: disable=logging-fstring-interpolation
            f"finish processing get_status for device {type_device} "
            f"from {member_type} with id {member_id}"
        )
        response = ConnectionStatus(token=device.id)

        if device.field_exists("healthy"):
            response.healthy = device.healthy
        if device.field_exists("user_id"):
            response.account_id = device.user_id
        if device.field_exists("expires_in"):
            response.subscription = Subscription(expires_in=device.expires_in)
        if type_device == ActivityDevice.TRANSTEK and device.field_exists(
            "last_status_report"
        ):
            response.device_metadata = {
                "battery": device.last_status_report.get("status", {}).get(
                    "bat"
                ),
                "signal": device.last_status_report.get("status", {}).get(
                    "sig"
                ),
            }

        return response

    raise HTTPException(
        404, "No device found linked to the provided member id"
    )


@router.get("/get_all_status")
async def get_all_devices_status(
    member_type: str,
    member_id: str,
) -> list[DeviceStatus]:
    """
    Route to get the connection status of all user devices.
    Does NOT raise exception is the member does not exist.
    """
    logger.info(
        "start processing get_all_devices_status for "
        f"{member_type} with id {member_id}"
    )

    rpm_member = await User.fetch_member(member_type, member_id)
    response = await UserDeviceService.fetch_device_statuses(rpm_member)

    return response


@router.post("/get_status_by_members")
async def get_status_by_members(
    request: Request, request_data: StatusRequest
) -> list[MemberDeviceStatus]:
    """Returns a list of device statuses for each member of the member list given as input"""
    response: list[MemberDeviceStatus] = []

    for member_id in request_data.members:
        rpm_member = await User.fetch_member(
            request_data.member_type.value, member_id
        )
        statuses = await UserDeviceService.fetch_device_statuses(rpm_member)
        member_device_status = MemberDeviceStatus(
            member_id=member_id, devices=statuses
        )
        if rpm_member:
            member_device_status.rpm_member_id = rpm_member.id

        response.append(member_device_status)

    return response


@router.get("/get_code")
async def get_code(
    type_device: ActivityDevice,
    mail: str,
    member_type: str,
    member_id: str,
    site: str = "",
    sync_start_date: int = None,
) -> AuthResp | ConnectionToken:
    """
    Route to get the auth_url from devices
    """
    auth_response = {"error": None}
    logger.info(  # pylint: disable=logging-fstring-interpolation
        f"start processing get_code for device {type_device.value} "
        f"from {member_type} with id '{member_id}'"
    )
    user = User(email=mail, device=type_device)
    device = await user.related_device()

    if device and check_health(device):
        logger.info(  # pylint: disable=logging-fstring-interpolation
            f"stop processing get_code, device with healthy connection already exists with id {device.id}"
        )
        return ConnectionToken(
            token=device.id,
            healthy=device.healthy if device.field_exists("healthy") else None,
            account_id=(
                device.user_id if device.field_exists("user_id") else None
            ),
        )

    # delete unhealthy connection to allow users to reconnect
    if device:
        await device.delete()

    member = await user.map_to_platform(
        external_type=member_type, external_id=member_id
    )

    member_state = (
        await MemberState.filter(member=member)
        .order_by("-created_at")
        .limit(1)
        .first()
    )

    if not member_state:
        member_state = MemberState(
            id=uuid4(), member=member, state=generate_unique_state()
        )

    uri = f"email={mail}"
    redirect_uri = ""

    if type_device == type_device.FITBIT:
        fitbit_client = FitbitLoader(
            client_id=settings.FITBIT_CLIENT_ID,
            client_secret=settings.FITBIT_CLIENT_SECRET,
            redirect_uri=settings.FITBIT_REDIRECT_URI,
        )
        redirect_uri = f"{settings.FITBIT_REDIRECT_URI}?{uri}"
        fitbit_client.redirect_uri = redirect_uri
        auth_response = await fitbit_client.get_auth_page_url(
            state=member_state.state
        )

    if type_device == type_device.DEXCOM:
        dexcom_client = DexcomLoader(
            client_id=settings.DEXCOM_CLIENT_ID,
            client_secret=settings.DEXCOM_CLIENT_SECRET,
            redirect_uri=settings.DEXCOM_REDIRECT_URI,
        )
        redirect_uri = f"{settings.DEXCOM_REDIRECT_URI}?{uri}"
        dexcom_client.redirect_uri = redirect_uri
        auth_response = await dexcom_client.get_auth_page_url()

    if type_device == type_device.WITHINGS:
        withings_client = WithingsLoader(
            client_id=settings.WITHINGS_CLIENT_ID,
            client_secret=settings.WITHINGS_CUSTOMER_SECRET,
            redirect_uri=settings.WITHINGS_REDIRECT_URI,
            callback_uri=settings.WITHINGS_CALLBACK_URI,
            notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
            scope=settings.WITHINGS_SCOPE,
            state=settings.WITHINGS_STATE,
        )
        auth_response = await withings_client.get_auth_page_url(
            state=member_state.state
        )

    if auth_response["error"]:
        logger.exception(
            f"Failed to get auth url for withings: {auth_response['error']}"
        )
        raise HTTPException(500)

    code_verifier = (
        auth_response["code_verifier"]
        if "code_verifier" in auth_response
        else ""
    )
    member_state.redirect_uri = redirect_uri
    member_state.code_verifier = code_verifier
    member_state.sync_start_date = (
        pendulum.from_timestamp(sync_start_date) if sync_start_date else None
    )

    member_state.redirect_uri = site
    await member_state.save()

    logger.info(  # pylint: disable=logging-fstring-interpolation
        f"stop processing get_code for device {type_device} from {member_type} with id {member_id}"
    )
    return AuthResp(auth_url=auth_response["auth_url"])


async def sync_user(request_data: SyncRequets, correlation_id: str):
    synced = False

    member = await Member.get_by_platform(
        platform_type=request_data.member_type.value,
        platform_id=request_data.member_id,
    )
    if not member:
        raise HTTPException(status_code=400, detail="Member not found")

    try:
        if request_data.type_device == ActivityDevice.WITHINGS:
            withings_client = WithingsLoader(
                client_id=settings.WITHINGS_CLIENT_ID,
                client_secret=settings.WITHINGS_CUSTOMER_SECRET,
                redirect_uri=settings.WITHINGS_REDIRECT_URI,
                callback_uri=settings.WITHINGS_CALLBACK_URI,
                notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
                scope=settings.WITHINGS_SCOPE,
                state=settings.WITHINGS_STATE,
            )
            member_withings = MemberWithings(
                member=member, client=withings_client
            )

            synced = await member_withings.sync_device(
                correlation_id=correlation_id,
                start_date=request_data.start_date,
            )

        if request_data.type_device == ActivityDevice.FITBIT:
            fitbit_client = FitbitLoader(
                client_id=settings.FITBIT_CLIENT_ID,
                client_secret=settings.FITBIT_CLIENT_SECRET,
                redirect_uri=settings.FITBIT_REDIRECT_URI,
            )
            member_fitbit = MemberFitbit(member=member, client=fitbit_client)

            synced = await member_fitbit.sync_device(
                correlation_id=correlation_id,
                start_date=request_data.start_date,
            )

        return {"synced": synced}
    except Exception as exc:
        logger.error(  # pylint: disable=logging-fstring-interpolation
            f"failed processing sync for device: {request_data.type_device} from platform: "
            f"{request_data.member_type} with platform_id: {request_data.member_id}",
            exc_info=exc,
        )
        error = f"sync failed, {exc}"
        raise HTTPException(status_code=500, detail=error) from exc


@router.post("/sync")
async def sync_api(request: Request, request_data: SyncRequets):
    """Depending on the device type will sync the data from the device"""
    correlation_id = request.headers.get(
        "X-Request-ID", str(pendulum.now().int_timestamp)
    )
    return await sync_user(request_data, correlation_id)


async def disconnect_user(request: DisconnectRequets):
    """Depending on the device type will remove the device and integration for it"""
    disconnected = False
    member = await Member.get_by_platform(
        platform_type=request.member_type.value, platform_id=request.member_id
    )
    if member is None:
        raise HTTPException(status_code=400, detail="Member not found")

    try:
        if request.type_device == ActivityDevice.WITHINGS:
            withings_client = WithingsLoader(
                client_id=settings.WITHINGS_CLIENT_ID,
                client_secret=settings.WITHINGS_CUSTOMER_SECRET,
                redirect_uri=settings.WITHINGS_REDIRECT_URI,
                callback_uri=settings.WITHINGS_CALLBACK_URI,
                notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
                scope=settings.WITHINGS_SCOPE,
                state=settings.WITHINGS_STATE,
            )
            member_withings = MemberWithings(
                member=member, client=withings_client
            )
            disconnected = await member_withings.disconnect_device()
        if request.type_device == ActivityDevice.FITBIT:
            fitbit_client = FitbitLoader(
                client_id=settings.FITBIT_CLIENT_ID,
                client_secret=settings.FITBIT_CLIENT_SECRET,
                redirect_uri=settings.FITBIT_REDIRECT_URI,
            )
            member_fitbit = MemberFitbit(member=member, client=fitbit_client)
            disconnected = await member_fitbit.disconnect_device()
        if request.type_device == ActivityDevice.DEXCOM:
            await Dexcom.filter(member=member).delete()
            disconnected = True

        if request.type_device == ActivityDevice.TRANSTEK:
            transtek = await Transtek.filter(
                member=member, id=request.device_id
            ).first()
            if transtek:
                await MemberDevicesRepository.disconnect_device(
                    member_id=member.id, external_id=transtek.device_id
                )
                await transtek.delete()
                disconnected = True

        return {"disconnected": disconnected}
    except Exception as exc:
        logger.error(  # pylint: disable=logging-fstring-interpolation
            f"failed processing disconnect for device: {request.type_device.value} "
            f"from platform: {request.member_type} and platform_id: {request.member_id}",
            exc_info=exc,
        )
        error = f"disconnect failed: {exc}"
        raise HTTPException(status_code=500, detail=error) from exc


@router.post("/disconnect")
async def disconnect_api(request: DisconnectRequets):
    """Depending on the device type will remove the device and integration for it"""
    return await disconnect_user(request)
