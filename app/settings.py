from functools import lru_cache
from typing import Any
from urllib.parse import quote_plus
from ciba_iot_etl.settings import IOTSettings
from enum import Enum


class ENV(str, Enum):
    DEV = "dev"
    PROD = "prod"
    LOCAL = "local"
    STG = "stage"
    TEST = "test"


class Settings(IOTSettings):
    """Project settings class.

    All the vars with default values here are initialized from env variables.
    If env var is not specified, default value will be used.
    """

    PROJECT_NAME: str = "CibaHealth RPM registration API"
    DEBUG: bool = False
    VERSION: str = "1.4.4"
    ENV: str = ENV.DEV

    POSTGRES_USER: str = "rpm"
    POSTGRES_PASSWORD: str = "PhqoM*0ncFAea432@b*J%2PR@R#82n"
    POSTGRES_DB: str = "rpm_single"
    POSTGRES_HOST: str = (
        "ciba-core-psql.cb62yimskknr.us-east-2.rds.amazonaws.com"
    )
    POSTGRES_PORT: int = 5432

    ALLOW_ORIGINS: str = "http://localhost:3000"
    ALLOW_HEADERS: str = "*"
    ALLOW_METHODS: str = "*"

    UI_HOST: str = "http://localhost:3000"
    SENTRY_DSN: str = ""

    WITHINGS_CLIENT_ID: str = ""
    WITHINGS_CUSTOMER_SECRET: str = ""
    WITHINGS_STATE: str = ""
    WITHINGS_CALLBACK_URI: str = ""
    WITHINGS_NOTIFICATION_CALLBACK_URI: str = ""
    WITHINGS_SCOPE: str = (
        "user.info,user.activity,user.metrics,user.sleepevents"
    )
    WITHINGS_REDIRECT_URI: str = ""

    WITHINGS_NOTIFICATION_QUEUE: str = ""
    WITHINGS_DEMO_MODE: bool = False

    OURARING_CLIENT_ID: str = ""
    OURARING_CUSTOMER_SECRET: str = ""
    OURARING_REDIRECT_URI: str = "http://127.0.0.1:8001/oruraring/get_token"
    OURARING_STATE: str = "orara_state_random"
    API_KEYS: str = ""

    FITBIT_CLIENT_ID: str = ""
    FITBIT_CLIENT_SECRET: str = ""
    FITBIT_REDIRECT_URI: str = ""
    FITBIT_VERIFY: str = "22A4lp0VI5QHg48eYt3kWOvSVQXvwFaP61a0yso6QJzbU3AGO1GQeK3PfZ06WT3dwL380KrhmbJFksf0nX77q07g14bW6XeN8PVCp8K0608H41Au7vvtaWser4LR"
    FITBIT_STATE: str = "state_random"
    FITBIT_VERIFY_SUBSCRIPTION: str = ""
    FITBIT_SCOPE: str = "activity heartrate sleep temperature respiratory_rate cardio_fitness oxygen_saturation"

    DEXCOM_CLIENT_ID: str = ""
    DEXCOM_CLIENT_SECRET: str = ""
    DEXCOM_REDIRECT_URI: str = ""

    TRANSTEK_API_KEY: str = "ZGzFXdRltU6kNSdxO3O1P7XY40o0VPjR14EivQsU"
    TRANSTEK_CIBA_KEY: str = ""

    AIRFLOW_HOST: str = "http://localhost:8080/api/v1"
    AIRFLOW_USERNAME: str = "airflow"
    AIRFLOW_PASSWORD: str = "airflow"
    METRICS_USERNAME: str = "admin"
    METRICS_PASSWORD: str = "admin"

    SQS_REGION: str = "us-east-2"
    SQS_ENDPOINT_URL: str = (
        "http://sqs.us-east-1.localhost.localstack.cloud:4566"
    )
    SQS_DATA_NOTIFICATION_QUEUE: str = (
        "https://localhost.localstack.cloud:4566/000000000000/rpm-data"
    )

    PARTICIPANT_POSTGRES_USER: str = ""
    PARTICIPANT_POSTGRES_PASSWORD: str = ""
    PARTICIPANT_POSTGRES_DB: str = ""
    PARTICIPANT_POSTGRES_HOST: str = ""
    PARTICIPANT_POSTGRES_PORT: int = 5432

    @property
    def default_db_url(self) -> str:
        """Construct default database url."""
        return (
            f"postgres://"
            f"{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}"
            f"@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}"
            f"/{self.POSTGRES_DB}"
        )

    @property
    def docs_enabled(self) -> bool:
        """Enable docs only for not prod envs."""
        return self.ENV != ENV.PROD


@lru_cache()
def get_settings(**kwargs: Any) -> Settings:
    """Initialize settings."""
    return Settings(**kwargs)
