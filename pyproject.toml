[project]
name = "rpm-registration-service"
version = "1.4.4"

description = ""
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
    "aerich>=0.7.1",
    "apache-airflow-client>=2.3.0",
    "asgi-correlation-id>=4.3.4",
    "asyncpg>=0.30.0",
    "boto3>=1.34.106",
    "ciba-iot-etl",
    "ddtrace>=3.9.3",
    "debugpy>=1.8.9",
    "httpx>=0.27.0",
    "jinja2>=3.1.4",
    "loguru>=0.6.0",
    "mocker>=1.1.1",
    "pandas>=2.1.0",
    "pendulum>=3.0.0",
    "psycopg>=3.1.19",
    "psycopg-binary>=3.2.2",
    "psycopg-pool>=3.2.2",
    "pydantic>=2.9.2",
    "pydantic-settings>=2.6.0",
    "python-multipart>=0.0.9",
    "requests>=2.28.1",
    "sentry-sdk>=1.11.0",
    "sqlalchemy>=2.0.21",
    "stackprinter>=0.2.9",
    "starlette>=0.47.2",
    "starlette-exporter>=0.15.1",
    "tortoise-orm>=0.21.7",
    "uvicorn>=0.30.6",
]



[project.optional-dependencies]
test = [
    "anyio>=3.6.2",
    "black>=22.10.0",
    "flake8>=5.0.4",
    "isort>=5.10.1",
    "mypy>=0.990",
    "pylint>=2.15.5",
    "pytest>=7.2.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "ruff>=0.1.2",
]
dev = [
    "debugpy>=1.8.2"
]

[tool.uv]
dev-dependencies = [
    "commitizen>=3.29.0",
    "pre-commit>=3.8.0",
]

[tool.uv.sources]
ciba-iot-etl = { git = "ssh://**************/Cibahealth/ciba-iot-etl.git", rev = "main"}

[tool.aerich]
tortoise_orm = "app.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."


[tool.black]
line-length = 79
target-version = ['py312']


[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 79
include_trailing_comma = true


[tool.mypy]
python_version = '3.12'
exclude = ['^venv/$', '^\\.venv/$', "/test_.*\\.py$"]
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true


[[tool.mypy.overrides]]
module = [
    "requests",
]
ignore_missing_imports = true


[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra"
testpaths = [
    "tests",
]
asyncio_default_fixture_loop_scope = "session"
filterwarnings = [
    "ignore::DeprecationWarning",
]

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 79
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
