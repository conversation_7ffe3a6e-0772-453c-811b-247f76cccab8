from unittest.mock import MagicMock, patch, AsyncMock
from uuid import UUID

import pytest
from ciba_iot_etl.models.db.withings import Withings

from app.common.messages import (
    FETCH_DEVICES_FAILED,
    REFRESH_TOKEN_FAILED,
    FETCH_MEASURES_FAILED,
    INVALID_FETCH_START_DATE,
)
from app.services.data_fetch.withings import WithingsDataFetcher

LAST_SYNC_MODULE = "app.services.data_fetch.withings.get_last_sync_by_vendor"
test_member_id = UUID("43f32830-df32-4f85-b643-70f1e593b2f3")
test_connection = MagicMock(spec=Withings)
test_connection.id = UUID("9a8a472f-06bd-48f8-be3f-72c877bbcf7f")
test_connection.refresh_token = "refresh_token"
test_connection.access_token = "access_token"
test_fetcher = WithingsDataFetcher(test_member_id, test_connection)


@pytest.mark.asyncio
async def test_refresh_tokens_with_error():
    """
    refresh_tokens should raise a RuntimeError
    when the refresh call fails.
    """
    with patch(
        "app.services.data_fetch.withings.WithingsLoader.refresh_token",
        new_callable=AsyncMock,
        return_value=None,
    ):
        with pytest.raises(RuntimeError) as expected_error:
            await test_fetcher.refresh_tokens()

        assert expected_error.value.args[0] == REFRESH_TOKEN_FAILED


@pytest.mark.asyncio
async def test_get_devices_with_expired_token():
    """
    get_devices should return a devices list refreshing token in the process.
    """
    test_refresh_response = {
        "status": 0,
        "body": {
            "access_token": "new_token",
            "refresh_token": "new_refresh",
            "expires_in": 3600,
        },
    }
    test_devices_response = {
        "status": 0,
        "body": {"devices": []},
    }
    test_connection.is_access_token_expired.return_value = True

    with (
        patch(
            "app.services.data_fetch.withings.WithingsLoader.refresh_token",
            new_callable=AsyncMock,
            return_value=test_refresh_response,
        ),
        patch(
            "app.services.data_fetch.withings.WithingsLoader.get_user_devices",
            new_callable=AsyncMock,
            return_value=test_devices_response,
        ),
        patch(
            "app.services.data_fetch.withings.Withings.update_tokens",
            new_callable=AsyncMock,
            return_value=test_connection,
        ) as mock_update,
    ):
        actual_values = await test_fetcher.get_devices()

        assert actual_values == []
        mock_update.assert_awaited_once_with(
            test_connection.id,
            test_refresh_response["body"]["access_token"],
            test_refresh_response["body"]["refresh_token"],
            test_refresh_response["body"]["expires_in"],
        )


@pytest.mark.asyncio
async def test_get_devices_with_error():
    """
    get_devices should raise a RuntimeError
    when the devices response is not ok.
    """
    test_devices_response = {
        "status": 1,
        "error": "test_error",
    }
    test_connection.is_access_token_expired.return_value = False

    with patch(
        "app.services.data_fetch.withings.WithingsLoader.get_user_devices",
        new_callable=AsyncMock,
        return_value=test_devices_response,
    ):
        with pytest.raises(RuntimeError) as expected_error:
            await test_fetcher.get_devices()

        assert expected_error.value.args[0] == FETCH_DEVICES_FAILED


@pytest.mark.asyncio
async def test_get_measures_with_sync_start_error():
    """
    get_measures should raise a ValueError
    when it's not possible to determine the fetch start date.
    """

    with patch(
        LAST_SYNC_MODULE,
        new_callable=AsyncMock,
        return_value=None,
    ):
        with pytest.raises(ValueError) as expected_error:
            await test_fetcher.get_measures(None)

        assert expected_error.value.args[0] == INVALID_FETCH_START_DATE


@pytest.mark.asyncio
async def test_get_measures_with_call_error():
    """
    get_measures should raise a RuntimeError
    when the measures call fails.
    """
    test_connection.is_access_token_expired.return_value = False
    test_response = MagicMock()
    test_response.status = 316
    test_response.error = "invalid user"

    with (
        patch(
            "app.services.data_fetch.withings.WithingsLoader.get_user_measure",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
        patch(
            LAST_SYNC_MODULE,
            new_callable=AsyncMock,
            return_value=16000,
        ),
    ):
        with pytest.raises(RuntimeError) as expected_error:
            await test_fetcher.get_measures()

        assert expected_error.value.args[0] == FETCH_MEASURES_FAILED


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_start, test_last_sync",
    [
        (16500, None),
        (18000, 19000),
    ],
)
async def test_get_measures_success(test_start, test_last_sync):
    """
    get_measures should return a list.
    """
    test_connection.is_access_token_expired.return_value = True
    test_response = MagicMock()
    test_response.status = 0
    test_body = MagicMock()
    test_body.measuregrps = []
    test_response.body = test_body

    with (
        patch(
            "app.services.data_fetch.withings.WithingsLoader.get_user_measure",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
        patch(
            LAST_SYNC_MODULE,
            new_callable=AsyncMock,
            return_value=test_last_sync,
        ),
        patch.object(test_fetcher, "refresh_tokens", new_callable=AsyncMock),
    ):
        actual_value = await test_fetcher.get_measures(test_start)

        assert actual_value == []
