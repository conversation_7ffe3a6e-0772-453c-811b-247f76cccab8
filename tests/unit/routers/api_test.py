from datetime import datetime
from unittest.mock import Async<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from uuid import uuid4

import pendulum
import pytest
from fastapi import Request
from fastapi.exceptions import HTTPException
from ciba_iot_etl.models.pydantic.common import ActivityDevice
from ciba_iot_etl.models.db.withings import Withings

from app.common.messages import MEMBER_NOT_FOUND
from app.pydantic_model.base import AuthResp
from app.pydantic_model.device_api import (
    ConnectionStatus,
    ConnectionToken,
    DeviceStatus,
    DeviceStatusEnum,
)
from app.pydantic_model.measures_api import Device, LatestMeasuresResponse
from app.routers.api import (
    get_code,
    get_status,
    get_all_devices_status,
    get_status_by_members,
)
from app.routers.measures import get_latest_data
from app.services.user import User
from app.services.withings import MemberWithings
from app.pydantic_model.device_api import MemberDeviceStatus
from app.routers.requests.api_request import StatusRequest
from tests.unit.common import TEST_EMAIL, TEST_URL

GET_BY_PLATFORM = "app.routers.measures.Member.get_by_platform"
test_member_id = uuid4()
test_exception = Exception("Test exception")
test_participant_type = "participant"


# Mock request object
class MockRequest(Request):
    def __init__(self):
        scope = {
            "type": "http",
            "headers": [(b"x-request-id", b"test-correlation-id")],
        }
        super().__init__(scope)


@pytest.fixture
def mock_member_state_model():
    model_mock = MagicMock()
    filter_mock = MagicMock()
    order_mock = MagicMock()
    limit_mock = MagicMock()
    model_mock.filter.return_value = filter_mock
    filter_mock.order_by.return_value = order_mock
    order_mock.limit.return_value = limit_mock

    actions = {
        "filter": filter_mock,
        "order_by": order_mock,
        "limit": limit_mock,
    }

    return model_mock, actions


@pytest.fixture
def mock_member():
    """
    Configure a mock member for testing purposes
    """
    test_member = Mock()
    test_member.id = test_member_id
    test_member.email = TEST_EMAIL

    return test_member


@pytest.fixture()
def mock_device():
    """
    Configure a mock device for testing purposes
    """
    test_device = Mock(spec=Withings)
    test_device.id = uuid4()
    test_device.healthy = True
    test_device.user_id = str(test_member_id)
    test_device.expires_in = **********
    test_device.updated_at = datetime.now()

    return test_device


@pytest.fixture(params=[None, MagicMock(spec=Withings)])
def mock_falsy_device(request):
    """Configure a None or unhealthy mock device."""
    test_device = request.param

    if test_device:
        test_device.delete = AsyncMock()
        test_device.healthy = False

    return test_device


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_status_1(mock_get_by_platform):
    """
    get_status should raise an HTTPException with status 500
    when the member data raises an exception.
    """
    mock_get_by_platform.side_effect = test_exception

    with pytest.raises(HTTPException) as error:
        await get_status(
            ActivityDevice.WITHINGS, "patient", str(test_member_id)
        )

    assert error.type is HTTPException
    assert error.value.status_code == 500
    assert error.value.detail == "Member data error: Test exception"


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_status_2(mock_get_by_platform):
    """
    get_status should raise an HTTPException with status 404
    when the provided member id is not present on the database.
    """
    mock_get_by_platform.return_value = None

    with pytest.raises(HTTPException) as error:
        await get_status(
            ActivityDevice.FITBIT, "participant", str(test_member_id)
        )

    assert error.type is HTTPException
    assert error.value.status_code == 404
    assert error.value.detail == "No member found with the provided id"


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
@patch.object(User, "related_device")
async def test_get_status_3(
    mock_related_device, mock_get_by_platform, mock_member
):
    """
    get_status should raise an HTTPException with status 500
    when the device data query raises an exception.
    """
    mock_get_by_platform.return_value = mock_member
    mock_related_device.side_effect = test_exception

    with pytest.raises(HTTPException) as error:
        await get_status(
            ActivityDevice.DEXCOM, "participant", str(test_member_id)
        )

    assert error.type is HTTPException
    assert error.value.status_code == 500
    assert error.value.detail == "Device data error: Test exception"


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
@patch.object(User, "related_device")
async def test_get_status_4(
    mock_related_device, mock_get_by_platform, mock_member
):
    """
    get_status should raise an HTTPException with status 404
    when the provided member has no devices linked.
    """
    mock_get_by_platform.return_value = mock_member
    mock_related_device.return_value = None

    with pytest.raises(HTTPException) as error:
        await get_status(
            ActivityDevice.OURARING, "participant", str(test_member_id)
        )

    assert error.type is HTTPException
    assert error.value.status_code == 404
    assert (
        error.value.detail
        == "No device found linked to the provided member id"
    )


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
@patch.object(User, "related_device")
async def test_get_status_5(
    mock_related_device, mock_get_by_platform, mock_member, mock_device
):
    """
    get_status should return a dictionary with the following keys:
    token, healthy, user_id, expires_in.
    """
    mock_get_by_platform.return_value = mock_member
    mock_related_device.return_value = mock_device

    actual_value = await get_status(
        ActivityDevice.WITHINGS, "participant", str(test_member_id)
    )

    assert isinstance(actual_value, ConnectionStatus)
    assert actual_value.token == mock_device.id
    assert actual_value.healthy == mock_device.healthy
    assert actual_value.account_id == mock_device.user_id
    assert actual_value.subscription.expires_in == mock_device.expires_in


class DummyMemberState:
    def __init__(self, sync_start_date):
        self.sync_start_date = sync_start_date
        self.state = 0
        self.save = self._save

    async def _save(self):
        return ""


TEST_MEMBER_STATE_MODEL = DummyMemberState(
    sync_start_date=pendulum.now().subtract(days=30)
)


class DummyQuery:
    def __init__(self, result):
        self.result = result

    async def get_or_none(self):
        # Return the dummy result asynchronously
        return self.result

    async def first(self):
        return self.result

    async def filter(self):
        return self.result


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.extract.withings_api.core.WithingsLoader.get_auth_page_url",
    new_callable=AsyncMock,
)
@patch.object(User, "map_to_platform")
@patch.object(User, "related_device")
async def test_get_code_with_auth_url_response(
    mock_related_device,
    mock_map_to_platform,
    mock_get_auth_code,
    mock_member,
    mock_falsy_device,
    mock_member_state_model,
):
    """
    get_code should return an authentication url when
    the device is not present in the database
    """
    member_state_mock, member_state_actions = mock_member_state_model
    member_state_actions["limit"].first = AsyncMock(
        return_value=TEST_MEMBER_STATE_MODEL
    )
    test_auth_response = {
        "auth_url": "https://auth.test.com",
        "error": "",
        "code_challenge": "",
        "code_verifier": "test",
        "state": 12341,
        "redirect_uri": "https://redirect.test.com",
    }

    with (
        patch("app.routers.api.MemberState", member_state_mock),
        patch("app.routers.api.MemberState.save") as mocked_state_save,
    ):
        mock_map_to_platform.return_value = mock_member
        mock_related_device.return_value = mock_falsy_device
        mocked_state_save.return_value = ""
        mock_get_auth_code.return_value = test_auth_response

        actual_value = await get_code(
            ActivityDevice.WITHINGS,
            "<EMAIL>",
            "patient",
            str(test_member_id),
            TEST_URL,
            0,
        )

    assert isinstance(actual_value, AuthResp)
    assert actual_value.auth_url == test_auth_response["auth_url"]


@pytest.mark.asyncio
@patch.object(MemberWithings, "create_state")
@patch.object(User, "related_device")
async def test_get_code_with_existing_healthy_connection(
    mock_related_device,
    mock_create_state,
    mock_device,
):
    """
    get_code should return the token and the connection health if exists
    """
    mock_related_device.return_value = mock_device
    mock_create_state.return_value = "test_state"

    actual_value = await get_code(
        ActivityDevice.WITHINGS,
        TEST_EMAIL,
        "participant",
        str(test_member_id),
        TEST_URL,
    )

    assert isinstance(actual_value, ConnectionToken)
    assert actual_value.healthy is True


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_all_devices_status_1(mock_get_by_platform):
    """
    get_all_devices should NOT raise an HTTPException with status 500
    when the member data is invalid or not found.
    """
    mock_get_by_platform.side_effect = test_exception

    statuses: list[DeviceStatus] = await get_all_devices_status(
        "patient", str(test_member_id)
    )

    assert len(statuses) == len([d.value for d in ActivityDevice]) - 1

    for i in range(4):
        assert statuses[i].status is DeviceStatusEnum.NOT_CONNECTED


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
@patch.object(User, "related_device")
@patch("ciba_iot_etl.models.db.transtek.Transtek.filter")
async def test_get_all_devices_status_2(
    mock_transtek_filter, mock_related_device, mock_get_by_platform, mock_member, mock_device
):
    """
    get_status should return a dictionary with the following keys:
    token, healthy, user_id, expires_in.
    """
    from ciba_iot_etl.models.db.transtek import TranstekStatus

    mock_get_by_platform.return_value = mock_member
    mock_related_device.return_value = mock_device

    # Create a mock Transtek device that appears healthy/connected
    mock_transtek_device = Mock()
    mock_transtek_device.id = uuid4()
    mock_transtek_device.status = TranstekStatus.ACTIVE  # This makes it healthy
    mock_transtek_device.last_status_report = None  # No recent status needed for basic connection
    mock_transtek_device.device_id = "test_device"
    mock_transtek_device.imei = "123456789012345"
    mock_transtek_device.model = "test_model"
    mock_transtek_device.device_type = Mock()
    mock_transtek_device.device_type.value = "scale"
    mock_transtek_device.field_exists = Mock(return_value=True)
    mock_transtek_device.updated_at = datetime.now()

    # Mock Transtek filter to return the healthy device
    mock_transtek_filter.return_value.all = AsyncMock(return_value=[mock_transtek_device])

    all_devices: list[DeviceStatus] = await get_all_devices_status(
        "participant", str(test_member_id)
    )

    assert len(all_devices) == len([d.value for d in ActivityDevice]) - 1

    for i in range(4):
        device = all_devices[i]
        assert device.status.value == DeviceStatusEnum.CONNECTED.value
        assert hasattr(device, "last_updated")
        assert device.last_updated is None or isinstance(
            device.last_updated, datetime
        )


@pytest.mark.asyncio
@patch(
    "ciba_iot_etl.models.db.member.Member.get_by_platform",
    new_callable=AsyncMock,
)
@patch.object(User, "related_device")
async def test_get_status_by_members(
    mock_related_device, mock_get_by_platform, mock_member, mock_device
):
    """Test endpoint for get_status_by_members"""
    # Create test request
    test_request = StatusRequest(
        member_type="patient", members=["test_member_1", "test_member_2"]
    )

    mock_get_by_platform.return_value = mock_member
    mock_related_device.return_value = mock_device
    mock_request = MockRequest()
    # Call the endpoint with test data
    result = await get_status_by_members(mock_request, test_request)

    # Verify response structure
    assert isinstance(result, list)
    assert len(result) == 2
    assert all(isinstance(item, MemberDeviceStatus) for item in result)
    assert result[0].member_id == "test_member_1"
    assert result[1].member_id == "test_member_2"


@pytest.mark.asyncio
async def test_get_latest_data_with_member_not_found():
    """
    get_latest_data should raise an HTTPException with status 404
    when the provided member is not found.
    """
    with patch(GET_BY_PLATFORM, new_callable=AsyncMock, return_value=None):
        with pytest.raises(HTTPException) as expected_error:
            await get_latest_data(
                ActivityDevice.WITHINGS,
                test_member_id,
                test_participant_type,
            )

        assert expected_error.value.status_code == 404
        assert expected_error.value.detail == MEMBER_NOT_FOUND


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_error, expected_code",
    [
        (ValueError("Bad input"), 400),
        (RuntimeError("Error on process"), 500),
    ],
)
async def test_get_latest_data_with_errors(
    test_error, expected_code, mock_member
):
    """
    get_latest_data should raise an HTTPException with error status
    when the processing raises a ValueError or a RuntimeError.
    """
    with (
        patch(
            GET_BY_PLATFORM, new_callable=AsyncMock, return_value=mock_member
        ),
        patch(
            "app.routers.measures.DataFetcherFactory.create",
            new_callable=AsyncMock,
            side_effect=test_error,
        ),
    ):
        with pytest.raises(HTTPException) as expected_error:
            await get_latest_data(
                ActivityDevice.WITHINGS,
                test_member_id,
                test_participant_type,
            )

        assert expected_error.value.status_code == expected_code


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_devices, expected_last_sync",
    [
        ([], None),
        (
            [
                Device(
                    id="1",
                    device_type="scale",
                    last_synced_at=pendulum.parse("2025-02-02"),
                )
            ],
            pendulum.parse("2025-02-02"),
        ),
    ],
)
async def test_get_latest_data_success(
    test_devices, expected_last_sync, mock_member
):
    """
    get_latest_data should raise an HTTPException with status 404
    when the provided member is not found.
    """
    test_service = MagicMock()
    test_service.get_devices = AsyncMock(return_value=test_devices)
    test_service.get_measures = AsyncMock(return_value=[])

    with (
        patch(
            GET_BY_PLATFORM, new_callable=AsyncMock, return_value=mock_member
        ),
        patch(
            "app.routers.measures.DataFetcherFactory.create",
            new_callable=AsyncMock,
            return_value=test_service,
        ),
    ):
        actual_value = await get_latest_data(
            ActivityDevice.WITHINGS,
            test_member_id,
            test_participant_type,
        )

        assert actual_value == LatestMeasuresResponse(
            devices=test_devices,
            measures=[],
            last_device_sync=expected_last_sync,
            last_ciba_sync=None,
        )
