from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID

import pendulum
import pytest
from ciba_iot_etl.extract.withings_api.common import MeasureGetMeasResponse
from fastapi import HTTPException, Request
from ciba_iot_etl.models.pydantic.common import ActivityDevice, PlatformType

from app.routers.manager import (
    get_users,
    get_user,
    get_user_data,
    get_user_data_api,
    ui_users,
    ui_user_detail,
    get_devices_health,
    ui_get_devices_health,
    check_health,
    check_devices_health,
    check_token_health,
    ui_get_device_info,
    ui_refresh_user_token,
    ui_get_user_data,
    ui_sync_user,
    ui_disconnect_user,
    _refresh_fitbit_token,
)
from app.routers.requests.api_request import DisconnectRequets

WITHINGS_MODULE = "app.routers.manager.Withings"
WITHINGS_REFRESH = "app.routers.manager.WithingsLoader.refresh_token"
WITHINGS_API_CALL = (
    "app.routers.manager.WithingsLoader._call_api_with_rate_limit_handling"
)
FITBIT_MODULE = "app.routers.manager.Fitbit"
FITBIT_REFRESH = "app.routers.manager.FitbitLoader.refresh_token"
FITBIT_ALL = "app.routers.manager.Fitbit.all"
FITBIT_CALL_API = (
    "app.routers.manager.FitbitLoader._call_api_with_rate_limit_handling"
)
GET_USER_DATA = "app.routers.manager.get_user_data"
MEMBER_PLATFORM_MODEL = "app.routers.manager.MemberPlatform"
MEMBER_GET = "app.routers.manager.Member.get"

test_member = MagicMock()
test_member.id = UUID("6b466245-c321-408f-9a9c-e85ff4a46ba2")
test_member.email = "<EMAIL>"
test_request = MagicMock(spec=Request)


async def mock_async_property(value):
    return value


def get_measure_mock(name: str) -> MagicMock:
    measure_model_mock = MagicMock()
    measure_model_mock.__name__ = name
    order_by_mock = MagicMock()
    filter_mock = MagicMock()
    filter_mock.order_by.return_value = order_by_mock
    measure_model_mock.filter.return_value = filter_mock
    order_by_mock.limit = AsyncMock(return_value=[])

    return measure_model_mock


@pytest.fixture
def mock_member_model():
    mock_member_model = MagicMock()
    mock_filter = MagicMock()
    mock_all = MagicMock()
    mock_member_model.filter.return_value = mock_filter
    mock_member_model.all.return_value = mock_all

    actions = {
        "all": mock_all,
        "filter": mock_filter,
    }

    return mock_member_model, actions


@pytest.fixture
def mock_device_model():
    mock_device_model = MagicMock()
    mock_all_result = AsyncMock()
    mock_filter = MagicMock()
    # Set up the filter chain properly: filter() returns an object with first() method
    mock_filter.first = AsyncMock()
    mock_device_model.all = AsyncMock(return_value=mock_all_result)
    mock_device_model.filter.return_value = mock_filter

    actions = {
        "all": mock_all_result,
        "filter": mock_filter,
    }

    return mock_device_model, actions


@pytest.fixture
def mock_member_platform_model():
    member_platform_mock = MagicMock()
    member_platform_filter_mock = MagicMock()
    member_platform_mock.filter.return_value = member_platform_filter_mock

    actions = {"filter": member_platform_filter_mock}

    return member_platform_mock, actions


@pytest.fixture
def mock_templates():
    templates_mock = MagicMock()
    templates_mock.TemplateResponse = MagicMock()

    return templates_mock


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module",
    [
        (ActivityDevice.WITHINGS, WITHINGS_MODULE),
        (ActivityDevice.FITBIT, FITBIT_MODULE),
    ],
)
async def test_get_users_with_activity_device(
    test_device, test_module, mock_member_model, mock_device_model
):
    """
    get_users should return a list of users
    """
    mock_model, mock_model_actions = mock_member_model
    mock_model_actions["all"].values = AsyncMock(
        return_value=[
            {"id": 1, "member_id": "member1"},
            {"id": 2, "member_id": "member2"},
        ]
    )

    member_mock, member_mock_actions = mock_member_model
    member_mock_actions["filter"].values = AsyncMock(return_value=[])

    with (
        patch(test_module, mock_model),
        patch("app.routers.manager.Member", member_mock),
    ):
        actual_value = await get_users(test_device)

        assert actual_value == []


@pytest.mark.asyncio
async def test_get_users_with_no_activity_device(mock_member_model):
    """
    get_users should return a list of all users.
    """
    test_response = [
        {"id": 1, "email": "<EMAIL>"},
        {"id": 2, "email": "<EMAIL>"},
    ]
    member_mock, member_mock_actions = mock_member_model
    member_mock_actions["all"].values = AsyncMock(return_value=test_response)

    with patch("app.routers.manager.Member", member_mock):
        actual_value = await get_users()

        assert actual_value == test_response


@pytest.mark.asyncio
async def test_get_user_with_not_found_error():
    """
    get_user should raise a not found exception
    when the provided email is not found.
    """
    with patch(
        MEMBER_GET,
        new_callable=AsyncMock,
        return_value=None,
    ):
        with pytest.raises(HTTPException) as expected_error:
            await get_user("<EMAIL>")

        assert expected_error.value.status_code == 404


@pytest.mark.asyncio
async def test_get_user_without_activities(
    mock_device_model, mock_member_platform_model
):
    """
    get_user should return user data without activities info
    when the actions flag is false.
    """
    test_device_data = MagicMock()
    test_device_data.id = "device_id"
    mock_device, mock_device_actions = mock_device_model
    mock_device_actions["filter"].first = AsyncMock(
        return_value=test_device_data
    )

    member_platform_mock, member_platform_actions = mock_member_platform_model
    member_platform_actions["filter"].all = AsyncMock(return_value=[])

    with (
        patch(WITHINGS_MODULE, mock_device),
        patch(FITBIT_MODULE, mock_device),
        patch(MEMBER_PLATFORM_MODEL, member_platform_mock),
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user(test_member.email)

        assert actual_value == {
            "id": test_member.id,
            "email": test_member.email,
            "activities": {},
            "member_platforms": [],
            "withings": test_device_data,
            "fitbit": test_device_data,
        }


@pytest.mark.asyncio
async def test_get_user_with_activities(
    mock_device_model, mock_member_platform_model
):
    """
    get_user should return user data including activities.
    """
    test_device_data = MagicMock()
    test_device_data.id = "device_id"
    mock_device, mock_device_actions = mock_device_model
    mock_device_actions["filter"].first = AsyncMock(
        return_value=test_device_data
    )

    test_platform_1 = MagicMock()
    test_platform_1.platform_type = PlatformType.participant.value
    test_platform_2 = MagicMock()
    test_platform_2.platform_type = PlatformType.patient.value
    member_platform_mock, member_platform_mock_actions = (
        mock_member_platform_model
    )
    member_platform_mock_actions["filter"].all = AsyncMock(
        return_value=[
            test_platform_1,
            test_platform_2,
        ]
    )

    with (
        patch(WITHINGS_MODULE, mock_device),
        patch(FITBIT_MODULE, mock_device),
        patch(MEMBER_PLATFORM_MODEL, member_platform_mock),
        patch("app.routers.manager.Activity", get_measure_mock("activity")),
        patch("app.routers.manager.Sleep", get_measure_mock("sleep")),
        patch("app.routers.manager.Weight", get_measure_mock("weight")),
        patch("app.routers.manager.HeartRate", get_measure_mock("heartRate")),
        patch(
            "app.routers.manager.BloodPressure",
            get_measure_mock("bloodPressure"),
        ),
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user(test_member.email, activities=True)

        assert actual_value == {
            "id": test_member.id,
            "email": test_member.email,
            "activities": {
                "patient_activity": [],
                "patient_bloodPressure": [],
                "patient_heartRate": [],
                "patient_sleep": [],
                "patient_weight": [],
            },
            "member_platforms": [test_platform_1, test_platform_2],
            "withings": test_device_data,
            "fitbit": test_device_data,
        }


@pytest.mark.asyncio
async def test_get_user_data_with_not_found():
    """
    get_user_data should raise 404 when provided email not found.
    """
    with patch(
        MEMBER_GET,
        new_callable=AsyncMock,
        return_value=None,
    ):
        with pytest.raises(HTTPException) as expected_error:
            await get_user_data("aaaaah!", ActivityDevice.WITHINGS)

        assert expected_error.value.status_code == 404


@pytest.mark.asyncio
async def test_get_user_data_with_invalid_device():
    """
    get_user_data should raise 400 when provided device is not supported.
    """
    with patch(
        MEMBER_GET,
        new_callable=AsyncMock,
        return_value=test_member,
    ):
        with pytest.raises(HTTPException) as expected_error:
            await get_user_data("<EMAIL>", ActivityDevice.DEXCOM)

        assert expected_error.value.status_code == 400


@pytest.mark.asyncio
async def test_get_user_data_for_fitbit(mock_device_model):
    """
    get_user_data should the member fitbit data.
    """
    test_fitbit = MagicMock()
    test_fitbit.id = "fitbit_id"
    test_fitbit.access_token = "fitbit_token"
    is_expired_mock = MagicMock(return_value=False)
    test_fitbit.is_access_token_expired = is_expired_mock
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_fitbit)

    with (
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            "app.routers.manager.fetch_fitbit_data",
            new_callable=AsyncMock,
            return_value={},
        ) as fetch_mock,
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user_data(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value == {}
        fetch_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_user_data_for_withings(mock_device_model):
    """
    get_user_data should return the member withings data.
    """
    test_withings = MagicMock()
    test_withings.id = "withings_id"
    test_withings.access_token = "test_token"
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(
        return_value=test_withings
    )
    test_withings_response = MeasureGetMeasResponse(
        measuregrps=[],
        timezone="America/New_York",
        updatetime=pendulum.parse("2025-01-10"),
    )

    with (
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            "app.routers.manager.process_user_message",
            new_callable=AsyncMock,
            return_value=test_withings_response,
        ) as process_mock,
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user_data(
            test_member.email,
            ActivityDevice.WITHINGS,
            pendulum.parse("2025-01-01"),
            pendulum.parse("2025-01-15"),
        )

        assert actual_value == {
            "blood_pressure_measurements": [],
            "heart_rate_measurements": [],
            "weight_measurements": [],
        }
        process_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_user_data_api(mock_device_model):
    """
    get_user_data_api should call get_user_data with the provided parameters.
    """
    with patch(
        GET_USER_DATA, new_callable=AsyncMock, return_value={}
    ) as get_data_mock:
        test_start = pendulum.parse("2025-02-20")
        test_end = pendulum.parse("2025-02-28")
        actual_value = await get_user_data_api(
            test_member.email,
            ActivityDevice.WITHINGS,
            test_start,
            test_end,
        )

        assert actual_value == {}
        get_data_mock.assert_awaited_once_with(
            test_member.email,
            ActivityDevice.WITHINGS,
            test_start,
            test_end,
        )


@pytest.mark.asyncio
async def test_ui_user(mock_templates):
    """
    ui_user should return the user template with the users' data.
    """
    test_response = [{"id": "1", "email": "<EMAIL>"}]

    with (
        patch("app.routers.manager.templates", mock_templates),
        patch(
            "app.routers.manager.get_users",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        await ui_users(test_request)

        mock_templates.TemplateResponse.assert_called_once_with(
            "users.html",
            {"request": test_request, "users": test_response},
        )


@pytest.mark.asyncio
async def test_ui_user_detail(mock_templates):
    """
    ui_user_detail should return the user detail template with the provider user data.
    """
    test_response = {"id": test_member.id, "email": test_member.email}

    with (
        patch("app.routers.manager.templates", mock_templates),
        patch(
            "app.routers.manager.get_user",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        await ui_user_detail(test_request, test_member.email)

        mock_templates.TemplateResponse.assert_called_once_with(
            "user_detail.html",
            {"request": test_request, "user": test_response},
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module",
    [
        (ActivityDevice.WITHINGS, WITHINGS_MODULE),
        (ActivityDevice.FITBIT, FITBIT_MODULE),
    ],
)
async def test_get_devices_health(test_device, test_module, mock_device_model):
    """
    get_devices_health should return the information of the devices
    with unhealthy connections.
    """
    test_return_device = MagicMock()
    test_return_device.member = mock_async_property(test_member)
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].all = AsyncMock(
        return_value=[test_return_device]
    )

    with patch(test_module, device_mock):
        actual_value = await get_devices_health(test_device)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_ui_get_devices_health():
    """
    ui_get_devices_health should call get_devices_health with the provided parameters.
    """
    actual_value = await ui_get_devices_health(ActivityDevice.OURARING)

    assert actual_value == {}


@pytest.mark.asyncio
async def test_check_devices_health():
    """
    check_devices_health should call get_devices_health with the provided parameters.
    """
    with patch(
        "app.routers.manager.get_devices_health", new_callable=AsyncMock
    ) as get_devices_health_mock:
        await check_devices_health(ActivityDevice.FITBIT)

        get_devices_health_mock.assert_awaited_once_with(ActivityDevice.FITBIT)


@pytest.mark.asyncio
async def test_check_token_health_with_unsupported_device():
    """
    check_token_health should return empty device info
    when unsupported device is provided.
    """
    actual_value = await check_token_health(ActivityDevice.OURARING)

    assert actual_value == {}


@pytest.mark.asyncio
async def test_check_token_health_for_withings_with_error():
    """
    check_token_health should return withings devices with unhealthy
    connection status due to an error.
    """
    test_device = MagicMock()
    test_device.member = mock_async_property(test_member)

    with (
        patch(
            "app.routers.manager.Withings.all",
            new_callable=AsyncMock,
            return_value=[test_device],
        ),
        patch(GET_USER_DATA, new_callable=AsyncMock, side_effect=Exception()),
    ):
        actual_value = await check_token_health(ActivityDevice.WITHINGS)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_check_token_health_for_withings():
    """
    check_token_health should return withings devices health status connection.
    """
    test_device = MagicMock()
    test_device.member = mock_async_property(test_member)

    with (
        patch(
            "app.routers.manager.Withings.all",
            new_callable=AsyncMock,
            return_value=[test_device],
        ),
        patch(GET_USER_DATA, new_callable=AsyncMock) as get_data_mock,
    ):
        actual_value = await check_token_health(ActivityDevice.WITHINGS)

        assert actual_value == {test_member.email: True}
        get_data_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit_with_error():
    """
    check_token_health should return fitbit devices
    with unhealthy connection status due to an error.
    """
    test_device = MagicMock()
    test_device.member = mock_async_property(test_member)
    test_device.is_access_token_expired = MagicMock(return_value=False)

    with (
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(GET_USER_DATA, new_callable=AsyncMock, side_effect=Exception()),
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit_with_refreshing_error(
    mock_device_model,
):
    """
    check_token_health should return fitbit unhealthy connections
    when refreshing token is not successful.
    """
    test_device = MagicMock()
    test_device.id = "Test Id"
    test_device.refresh_token = "Test refresh"
    test_device.member = mock_async_property(test_member)
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].update = AsyncMock()
    test_device.is_access_token_expired = MagicMock(return_value=True)
    test_refresh_response = MagicMock()
    test_refresh_response.error = "invalid refresh token"

    with (
        patch(FITBIT_MODULE, fitbit_mock),
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(
            FITBIT_REFRESH,
            new_callable=AsyncMock,
            return_value=test_refresh_response,
        ),
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit_with_updating_error(
    mock_device_model,
):
    """
    check_token_health should return fitbit unhealthy connections
    when refreshing token is not successful.
    """
    test_device = MagicMock()
    test_device.id = "Test Id"
    test_device.refresh_token = "Test refresh"
    test_device.member = mock_async_property(test_member)
    test_device.is_access_token_expired = MagicMock(return_value=True)
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].update = AsyncMock()
    test_refresh_response = MagicMock()
    test_refresh_response.error = None
    test_refresh_response.access_token = "New access token"
    test_refresh_response.refresh_token = "New refresh token"
    test_refresh_response.expires_in = 7200

    with (
        patch(FITBIT_MODULE, fitbit_mock),
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(
            "app.routers.manager.Fitbit.update_tokens",
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
        patch(
            FITBIT_REFRESH,
            new_callable=AsyncMock,
            return_value=test_refresh_response,
        ),
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit():
    """
    check_token_health should return fitbit devices connection status.
    """
    test_device = MagicMock()
    test_device.member = mock_async_property(test_member)
    test_device.is_access_token_expired = MagicMock(return_value=False)

    with (
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(GET_USER_DATA, new_callable=AsyncMock) as get_data_mock,
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: True}
        get_data_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_check_health():
    """
    check_health should call check_token_health with the provided activity device.
    """
    with patch(
        "app.routers.manager.check_token_health", new_callable=AsyncMock
    ) as token_health_mock:
        await check_health(ActivityDevice.FITBIT)

        token_health_mock.assert_awaited_once_with(ActivityDevice.FITBIT)


@pytest.mark.asyncio
async def test_ui_get_device_info_with_user_not_fund():
    """
    ui_get_device_info should return unsuccessful response
    when no user found.
    """
    with patch(MEMBER_GET, new_callable=AsyncMock, return_value=None):
        actual_value = await ui_get_device_info(
            "random", ActivityDevice.WITHINGS
        )

        assert actual_value == {
            "success": False,
            "error": "Member not found: random",
        }


@pytest.mark.asyncio
async def test_ui_get_device_info_with_unsupported_device():
    """
    ui_get_device_info should return unsuccessful response
    when no supported device is provided.
    """
    with patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.OURARING
        )

        assert actual_value == {
            "success": False,
            "error": "Unsupported device type: ActivityDevice.OURARING",
        }


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module, error_prefix",
    [
        (ActivityDevice.FITBIT, FITBIT_MODULE, "Fitbit"),
        (ActivityDevice.WITHINGS, WITHINGS_MODULE, "Withings"),
    ],
)
async def test_ui_get_device_info_for_device_not_found(
    test_device, test_module, error_prefix, mock_device_model
):
    """
    ui_get_device_info should return unsuccessful response
    when activity device is not foud.
    """
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=None)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(test_module, device_mock),
    ):
        actual_value = await ui_get_device_info(test_member.email, test_device)

        assert actual_value == {
            "success": False,
            "error": f"{error_prefix} connection not found for member: {test_member.email}",
        }


@pytest.mark.asyncio
async def test_ui_get_device_info_for_withings_with_exception(
    mock_device_model,
):
    """
    ui_get_device_info should return unsuccessful response
    when withings device is not foud.
    """
    test_device = MagicMock()
    test_device.id = "withings_id"
    test_device.access_token = "withings_access"
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(return_value=test_device)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            WITHINGS_API_CALL,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value == {
            "success": False,
            "error": "Failed to get device info: ",
        }


@pytest.mark.asyncio
@pytest.mark.parametrize("test_error_code", [401, 601, 501])
async def test_ui_get_device_info_for_withings_with_errors(
    test_error_code, mock_device_model
):
    """
    ui_get_device_info should return unsuccessful response
    when withings device is not foud.
    """
    test_device = MagicMock()
    test_device.id = "withings_id"
    test_device.access_token = "withings_access"
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(return_value=test_device)
    test_response = {"status": test_error_code}

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            WITHINGS_API_CALL,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_get_device_info_for_withings_success(mock_device_model):
    """
    ui_get_device_info should return successful response
    including withings linked devices.
    """
    test_device = MagicMock()
    test_device.id = "withings_id"
    test_device.access_token = "withings_access"
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(return_value=test_device)
    test_response = {
        "status": 0,
        "body": {
            "devices": [
                {
                    "first_session_date": "a",
                    "last_session_date": "b",
                },
                {
                    "first_session_date": "1748754000",
                    "last_session_date": "1748840400",
                },
            ],
        },
    }

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            WITHINGS_API_CALL,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
        patch("app.routers.manager.Withings.expire_old_refresh_token", new_callable=AsyncMock),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is True
        assert actual_value["devices"] == [
            {
                "first_session_date": "a",
                "last_session_date": "b",
            },
            {
                "first_session_date": "2025-06-01 05:00:00",
                "last_session_date": "2025-06-02 05:00:00",
            },
        ]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_error",
    [
        {},
        {"type": "test", "message": "message"},
        {"type": "expired_token", "message": "token expired"},
        {"type": "invalid_token", "message": "token not valid"},
        {"type": "rate_limit_exceeded", "message": "limit exceeded"},
    ],
)
async def test_ui_get_device_info_for_fitbit_with_error(
    test_error, mock_device_model
):
    """
    ui_get_device_info should return unsuccessful response
    when the fitbit device is not found.
    """
    test_device = MagicMock()
    test_device.id = "fitbit_id"
    test_device.access_token = "fitbit_access"
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_device)
    test_response = {"error": test_error}

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            FITBIT_CALL_API,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_get_device_info_for_fitbit_with_exception(mock_device_model):
    """
    ui_get_device_info should return unsuccessful response
    when the fitbit call raises an exception.
    """
    test_device = MagicMock()
    test_device.access_token = "fitbit_access"
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_device)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            FITBIT_CALL_API,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value == {
            "success": False,
            "error": "Failed to get device info: ",
        }


fitbit_test_devices = [
    {"id": "1", "model": "scale"},
    {"id": "2", "model": "tracker"},
]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_response, expected_response",
    [
        ({}, []),
        (fitbit_test_devices, fitbit_test_devices),
    ],
)
async def test_ui_get_device_info_for_fitbit_success(
    test_response, expected_response, mock_device_model
):
    """
    ui_get_device_info should return successful response
    with fitbit devices list.
    """
    test_device = MagicMock()
    test_device.id = "fitbit_id"
    test_device.access_token = "fitbit_access"
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_device)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            FITBIT_CALL_API,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is True
        assert actual_value["devices"] == expected_response


@pytest.mark.asyncio
async def test_ui_refresh_user_token_with_member_not_found():
    """
    ui_refresh_user_token should return unsuccessful response
    when the provided user is not found.
    """
    with patch(MEMBER_GET, new_callable=AsyncMock, return_value=None):
        actual_value = await ui_refresh_user_token(
            "another test", ActivityDevice.OURARING
        )

        assert actual_value["success"] is False
        assert actual_value["error"] == "Member not found: another test"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module",
    [
        (ActivityDevice.FITBIT, FITBIT_MODULE),
        (ActivityDevice.WITHINGS, WITHINGS_MODULE),
    ],
)
async def test_ui_refresh_user_token_with_device_not_found(
    test_device, test_module, mock_device_model
):
    """
    ui_refresh_user_token should return unsuccessful response
    when the provided devices is not found.
    """
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=None)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(test_module, device_mock),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, test_device
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_withings_with_exception(
    mock_device_model,
):
    """
    ui_refresh_user_token should return unsuccessful response
    when withings refresh raises an exception.
    """
    test_withings = MagicMock()
    test_withings.refresh_token = "<PASSWORD>"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_withings)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, device_mock),
        patch(
            WITHINGS_REFRESH,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_response",
    [
        {"error": "invalid_grant"},
        {"error": "test_error"},
        {},
    ],
)
async def test_ui_refresh_user_token_for_withings_with_errors(
    test_response, mock_device_model
):
    """
    ui_refresh_user_token should return unsuccessful response
    when withings refresh token fails.
    """
    test_withings = MagicMock()
    test_withings.refresh_token = "<PASSWORD>"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_withings)
    device_mock_actions["filter"].update = AsyncMock()

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, device_mock),
        patch(
            WITHINGS_REFRESH,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_withings_success(mock_device_model):
    """
    ui_refresh_user_token should return successful response.
    """
    test_withings = MagicMock()
    test_withings.refresh_token = "withings_123"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_withings)
    device_mock_actions["filter"].update = AsyncMock()
    test_response = {
        "access_token": "withings_abc",
        "refresh_token": "withings_xyz",
        "expires_in": 10800,
    }
    test_updated = MagicMock()
    test_updated.access_token = test_response["access_token"]
    test_updated.refresh_token = test_response["refresh_token"]
    test_updated.expires_in = test_response["expires_in"]
    test_updated.expires_in = pendulum.parse("2025-05-31")

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, device_mock),
        patch(
            WITHINGS_REFRESH,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
        patch(
            "app.routers.manager.Withings.update_tokens",
            new_callable=AsyncMock,
            return_value=test_updated,
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is True
        assert (
            actual_value["message"] == "Withings token refreshed successfully"
        )


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_fitbit_with_exception(
    mock_device_model,
):
    """
    ui_refresh_user_token should return unsuccessful response
    when fitbit refresh raises an exception.
    """
    test_fitbit = MagicMock()
    test_fitbit.refresh_token = "something"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_fitbit)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, device_mock),
        patch(
            FITBIT_REFRESH,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_error, test_re_auth",
    [
        ("invalid_grant", True),
        ("fitbit_test_error", None),
    ],
)
async def test_ui_refresh_user_token_for_fitbit_with_errors(
    test_error, test_re_auth, mock_device_model
):
    """
    ui_refresh_user_token should return unsuccessful response
    when fitbit refresh presents errors.
    """
    test_fitbit = MagicMock()
    test_fitbit.refresh_token = "something else"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_fitbit)
    device_mock_actions["filter"].update = AsyncMock()
    test_response = MagicMock()
    test_response.error = test_error

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, device_mock),
        patch(
            FITBIT_REFRESH,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is False
        assert actual_value.get("requires_reauth") == test_re_auth


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_fitbit_success(mock_device_model):
    """
    ui_refresh_user_token should return a successful response.
    """
    test_fitbit = MagicMock()
    test_fitbit.refresh_token = "something else"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_fitbit)
    device_mock_actions["filter"].update = AsyncMock()
    test_response = MagicMock()
    test_response.error = None
    test_response.access_token = "fitbit_zero"
    test_response.refresh_token = "fitbit_alpha"
    test_response.access_token = 1800
    test_updated = MagicMock()
    test_updated.access_token = test_response.access_token
    test_updated.refresh_token = test_response.refresh_token
    test_updated.access_token_expires_at = pendulum.parse("2025-07-16")

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, device_mock),
        patch(
            FITBIT_REFRESH,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
        patch(
            "app.routers.manager.Fitbit.update_tokens",
            new_callable=AsyncMock,
            return_value=test_updated,
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is True
        assert actual_value["message"] == "Fitbit token refreshed successfully"


@pytest.mark.asyncio
async def test_ui_get_user_data():
    """
    ui_get_user_data should call get_user_data with correct arguments.
    """
    with patch(
        "app.routers.manager.get_user_data", new_callable=AsyncMock
    ) as mock_get_user_data:
        await ui_get_user_data(test_member.email, ActivityDevice.DEXCOM)

        mock_get_user_data.assert_awaited_once_with(
            test_member.email, ActivityDevice.DEXCOM, None, None
        )


@pytest.mark.asyncio
async def test_ui_sync_user(mock_member_platform_model):
    """
    ui_sync_user should call sync_user with correct arguments
    for every member platform.
    """
    test_platform_1 = MagicMock()
    test_platform_1.platform_id = "patient_1"
    test_platform_1.platform_type = "patient"
    test_platform_2 = MagicMock()
    test_platform_2.platform_id = "participant_1"
    test_platform_2.platform_type = "participant"
    platform_mock, platform_mock_actions = mock_member_platform_model
    platform_mock_actions["filter"].all = AsyncMock(
        return_value=[test_platform_1, test_platform_2]
    )

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(MEMBER_PLATFORM_MODEL, platform_mock),
        patch(
            "app.routers.manager.sync_user", new_callable=AsyncMock
        ) as sync_mock,
    ):
        await ui_sync_user(test_member.email, ActivityDevice.FITBIT)

        assert sync_mock.await_count == 2


@pytest.mark.asyncio
async def test_ui_disconnect_user(mock_member_platform_model):
    """
    ui_disconnect_user should call disconnect_user with correct arguments
    for every member platform.
    """
    test_platform = MagicMock()
    test_platform.platform_id = "my_id"
    test_platform.platform_type = "patient"
    platform_mock, platform_mock_actions = mock_member_platform_model
    platform_mock_actions["filter"].all = AsyncMock(
        return_value=[test_platform]
    )

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(MEMBER_PLATFORM_MODEL, platform_mock),
        patch(
            "app.routers.manager.disconnect_user", new_callable=AsyncMock
        ) as disconnect_mock,
    ):
        actual_value = await ui_disconnect_user(
            test_request, test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value == {"disconnected": True}
        disconnect_mock.assert_awaited_once_with(
            DisconnectRequets(
                member_id=str(test_platform.platform_id),
                type_device=ActivityDevice.FITBIT,
                member_type=test_platform.platform_type,
            )
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_error, expected_code",
    [
        ("invalid_grant", 401),
        ("test_revoke", 500),
    ],
)
async def test_refresh_fitbit_token_with_errors(
    test_error, expected_code, mock_device_model
):
    """
    _refresh_fitbit_token should raise HTTPError
    when the token refresh fails.
    """
    test_result = MagicMock()
    test_result.error = test_error
    test_service = MagicMock()
    test_service.refresh_token = AsyncMock(return_value=test_result)
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].update = AsyncMock()

    with patch(FITBIT_MODULE, device_mock):
        with pytest.raises(HTTPException) as expected_error:
            await _refresh_fitbit_token(MagicMock(), test_service)

        assert expected_error.value.status_code == expected_code
