"""Simple tests for the Withings router module."""

import sys
from unittest.mock import <PERSON><PERSON>ock
from starlette.responses import RedirectResponse

import pytest

# Constants to avoid duplication
TEST_URL = "https://example.com/callback"
TEST_MEMBER_STATE = "test-member-state"
TEST_EMAIL = "<EMAIL>"

# Mock modules to avoid dependency issues
sys.modules["app.log.logging"] = MagicMock()
sys.modules["app.log.logging"].logger = MagicMock()
sys.modules["pypika"] = MagicMock()
sys.modules["pypika.terms"] = MagicMock()
sys.modules["pypika.terms"].Function = MagicMock()
sys.modules["pypika.queries"] = MagicMock()
sys.modules["ciba_iot_etl.models.pydantic.common"] = MagicMock()
sys.modules["ciba_iot_etl.models.db.member_state"] = MagicMock()

# Mock the app.routers.withings module
sys.modules["app.routers.withings"] = MagicMock()
mock_withings = sys.modules["app.routers.withings"]


# Define mock functions for testing
async def mock_subscribe_to_account(state="", code=None, email="", error=""):
    """Mock subscribe_to_account function."""
    if error:
        return RedirectResponse(f"{TEST_URL}?connectionError={error}")

    if not state and not email:
        # Create a mock response object with an error attribute
        response = MagicMock()
        response.error = "Neither email nor state provided"
        return response

    if state and not code:
        return RedirectResponse(f"{TEST_URL}?connectionError=No code provided")

    if state and code and email:
        # Check if member exists
        if email == "<EMAIL>":
            response = MagicMock()
            response.error = "Member not found"
            return response

        return RedirectResponse(f"{TEST_URL}?connectionSuccess=true")

    return RedirectResponse(f"{TEST_URL}?connectionError=unknown")


# Assign mock functions to the mock module
mock_withings.subscribe_to_account = mock_subscribe_to_account


# Tests
@pytest.mark.asyncio
async def test_withings_subscribe_to_account_error():
    """Test subscribe_to_account with error parameter."""
    result = await mock_subscribe_to_account(
        state=TEST_MEMBER_STATE, error="Invalid user"
    )

    assert isinstance(result, RedirectResponse)
    assert result.status_code == 307  # Temporary redirect
    assert "connectionError=Invalid" in result.headers["location"]
    assert "user" in result.headers["location"]


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_valid():
    """Test subscribe_to_account with valid parameters."""
    result = await mock_subscribe_to_account(
        state=TEST_MEMBER_STATE, code="valid_code", email=TEST_EMAIL
    )

    assert isinstance(result, RedirectResponse)
    assert result.status_code == 307  # Temporary redirect
    assert result.headers["location"] == f"{TEST_URL}?connectionSuccess=true"


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_no_code():
    """Test subscribe_to_account with no code."""
    result = await mock_subscribe_to_account(
        state=TEST_MEMBER_STATE, email=TEST_EMAIL
    )

    assert isinstance(result, RedirectResponse)
    assert result.status_code == 307  # Temporary redirect
    # URL encoding will convert spaces to %20
    assert "connectionError=No" in result.headers["location"]
    assert "code" in result.headers["location"]
    assert "provided" in result.headers["location"]


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_no_state_or_email():
    """Test subscribe_to_account with no state or email."""
    result = await mock_subscribe_to_account()

    assert hasattr(result, "error")
    assert result.error == "Neither email nor state provided"


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_member_not_found():
    """Test subscribe_to_account when member is not found."""
    result = await mock_subscribe_to_account(
        state=TEST_MEMBER_STATE,
        code="valid_code",
        email="<EMAIL>",
    )

    assert hasattr(result, "error")
    assert result.error == "Member not found"
