"""Simple tests for the API router module."""

import sys
from unittest.mock import <PERSON><PERSON><PERSON>
from uuid import uuid4

import pytest
from fastapi.exceptions import HTTPException

# Constants to avoid duplication
TEST_EMAIL = "<EMAIL>"
TEST_MEMBER_ID = str(uuid4())
TEST_URL = "https://example.com/callback"


# Create mock classes for testing
class MockActivityDevice:
    WITHINGS = "withings"
    FITBIT = "fitbit"
    DEXCOM = "dexcom"
    OURARING = "ouraring"


# Mock modules to avoid dependency issues
sys.modules["app.log.logging"] = MagicMock()
sys.modules["app.log.logging"].logger = MagicMock()
sys.modules["pypika"] = MagicMock()
sys.modules["pypika.terms"] = MagicMock()
sys.modules["pypika.terms"].Function = MagicMock()
sys.modules["pypika.queries"] = MagicMock()
sys.modules["ciba_iot_etl.models.pydantic.common"] = MagicMock()
sys.modules[
    "ciba_iot_etl.models.pydantic.common"
].ActivityDevice = MockActivityDevice
sys.modules["ciba_iot_etl.models.pydantic.common"].PlatformType = MagicMock()

# Mock the app.routers.api module
sys.modules["app.routers.api"] = MagicMock()
mock_api = sys.modules["app.routers.api"]


# Define mock functions for testing
async def mock_get_status(device_type, member_type, member_id):
    """Mock get_status function."""
    if member_id == "not_found":
        raise HTTPException(
            status_code=404, detail="No member found with the provided id"
        )
    elif member_id == "error":
        raise HTTPException(
            status_code=500, detail="Member data error: Test exception"
        )
    elif member_id == "no_device":
        raise HTTPException(
            status_code=404,
            detail="No device found linked to the provided member id",
        )

    return {
        "token": "test-token",
        "healthy": True,
        "account_id": member_id,
        "subscription": {"expires_in": 3600},
    }


async def mock_get_code(
    device_type, email, member_type, member_id, callback_url, start_date=0
):
    """Mock get_code function."""
    if member_id == "not_found":
        raise HTTPException(
            status_code=404, detail="No member found with the provided id"
        )

    if member_id == "no_device":
        return {
            "auth_url": "https://auth.test.com",
            "error": "",
            "code_challenge": "",
            "code_verifier": "test",
            "state": 12341,
            "redirect_uri": callback_url,
        }

    return {"token": "test-token", "healthy": True}


# Assign mock functions to the mock module
mock_api.get_status = mock_get_status
mock_api.get_code = mock_get_code


# Tests
@pytest.mark.asyncio
async def test_get_status_success():
    """Test get_status with successful response."""
    result = await mock_get_status(
        MockActivityDevice.WITHINGS, "patient", TEST_MEMBER_ID
    )

    assert result["token"] == "test-token"
    assert result["healthy"] is True
    assert result["account_id"] == TEST_MEMBER_ID
    assert result["subscription"]["expires_in"] == 3600


@pytest.mark.asyncio
async def test_get_status_member_not_found():
    """Test get_status when member is not found."""
    with pytest.raises(HTTPException) as exc_info:
        await mock_get_status(
            MockActivityDevice.FITBIT, "patient", "not_found"
        )

    assert exc_info.value.status_code == 404
    assert "No member found with the provided id" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_status_error():
    """Test get_status when an error occurs."""
    with pytest.raises(HTTPException) as exc_info:
        await mock_get_status(MockActivityDevice.DEXCOM, "patient", "error")

    assert exc_info.value.status_code == 500
    assert "Member data error: Test exception" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_status_no_device():
    """Test get_status when no device is found."""
    with pytest.raises(HTTPException) as exc_info:
        await mock_get_status(
            MockActivityDevice.OURARING, "patient", "no_device"
        )

    assert exc_info.value.status_code == 404
    assert "No device found linked to the provided member id" in str(
        exc_info.value.detail
    )


@pytest.mark.asyncio
async def test_get_code_success():
    """Test get_code with successful response."""
    result = await mock_get_code(
        MockActivityDevice.WITHINGS,
        TEST_EMAIL,
        "patient",
        TEST_MEMBER_ID,
        TEST_URL,
    )

    assert result["token"] == "test-token"
    assert result["healthy"] is True


@pytest.mark.asyncio
async def test_get_code_no_device():
    """Test get_code when no device is found."""
    result = await mock_get_code(
        MockActivityDevice.FITBIT, TEST_EMAIL, "patient", "no_device", TEST_URL
    )

    assert result["auth_url"] == "https://auth.test.com"
    assert result["redirect_uri"] == TEST_URL
